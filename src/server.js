/**
 * VideoChat Couple - Server principale
 * Sistema completo con login, registrazione, videochat e gestione crediti
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const cors = require('cors');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: ["http://localhost:3001", "https://videochatcouple.com", "http://videochatcouple.com"],
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Configurazione middleware
app.use(cors({
  origin: ["http://localhost:3001", "https://videochatcouple.com", "http://videochatcouple.com"],
  credentials: true
}));
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minuti
  max: 100 // limite di 100 richieste per IP
});
app.use(limiter);

// Database in memoria (per semplicità)
const users = new Map();
const activeUsers = new Map();
const chatRooms = new Map();

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'videochat_couple_secret_2024';

// Middleware di autenticazione
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.sendStatus(401);
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) return res.sendStatus(403);
    req.user = user;
    next();
  });
};

// API Routes

// Registrazione utente
app.post('/api/register', async (req, res) => {
  try {
    const { email, password, ageVerified } = req.body;

    if (!email || !password || !ageVerified) {
      return res.status(400).json({ error: 'Tutti i campi sono obbligatori' });
    }

    if (users.has(email)) {
      return res.status(400).json({ error: 'Email già registrata' });
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const user = {
      id: Date.now().toString(),
      email,
      password: hashedPassword,
      credits: 10, // 10 crediti gratuiti
      createdAt: new Date(),
      isOnline: false
    };

    users.set(email, user);

    const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET);

    res.json({
      success: true,
      token,
      user: {
        id: user.id,
        email: user.email,
        credits: user.credits
      }
    });
  } catch (error) {
    res.status(500).json({ error: 'Errore del server' });
  }
});

// Login utente
app.post('/api/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ error: 'Email e password sono obbligatori' });
    }

    const user = users.get(email);
    if (!user) {
      return res.status(400).json({ error: 'Credenziali non valide' });
    }

    const validPassword = await bcrypt.compare(password, user.password);
    if (!validPassword) {
      return res.status(400).json({ error: 'Credenziali non valide' });
    }

    const token = jwt.sign({ id: user.id, email: user.email }, JWT_SECRET);

    res.json({
      success: true,
      token,
      user: {
        id: user.id,
        email: user.email,
        credits: user.credits
      }
    });
  } catch (error) {
    res.status(500).json({ error: 'Errore del server' });
  }
});

// Profilo utente
app.get('/api/profile', authenticateToken, (req, res) => {
  const user = users.get(req.user.email);
  if (!user) {
    return res.status(404).json({ error: 'Utente non trovato' });
  }

  res.json({
    id: user.id,
    email: user.email,
    credits: user.credits,
    isOnline: user.isOnline
  });
});

// Statistiche utenti online
app.get('/api/stats', (req, res) => {
  const onlineCount = Array.from(users.values()).filter(user => user.isOnline).length;
  res.json({
    onlineUsers: onlineCount,
    totalUsers: users.size
  });
});

// Gestione Socket.IO per videochat
io.on('connection', (socket) => {
  console.log('Nuovo utente connesso:', socket.id);

  // Autenticazione socket
  socket.on('authenticate', (token) => {
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      socket.userId = decoded.id;
      socket.userEmail = decoded.email;

      const user = users.get(decoded.email);
      if (user) {
        user.isOnline = true;
        activeUsers.set(socket.id, user);

        socket.emit('authenticated', {
          userId: user.id,
          email: user.email,
          credits: user.credits
        });

        // Notifica agli altri utenti
        socket.broadcast.emit('user-online', {
          userId: user.id,
          email: user.email
        });
      }
    } catch (error) {
      console.error('Errore autenticazione socket:', error);
      socket.emit('auth-error', 'Token non valido');
    }
  });

  // Richiesta di ricerca partner (nuovo evento)
  socket.on('find-match', (data) => {
    const user = activeUsers.get(socket.id);
    if (!user || user.credits < 1) {
      socket.emit('videochat-error', 'Crediti insufficienti');
      return;
    }

    // Cerca un partner disponibile
    const availableUsers = Array.from(activeUsers.entries())
      .filter(([socketId, u]) => socketId !== socket.id && u.credits >= 1);

    if (availableUsers.length === 0) {
      socket.emit('no-partners', 'Nessun partner disponibile al momento');
      return;
    }

    // Seleziona un partner casuale
    const [partnerSocketId, partnerUser] = availableUsers[Math.floor(Math.random() * availableUsers.length)];

    // Crea una stanza di chat
    const roomId = `room_${Date.now()}`;
    chatRooms.set(roomId, {
      users: [socket.id, partnerSocketId],
      createdAt: new Date()
    });

    // Unisci entrambi gli utenti alla stanza
    socket.join(roomId);
    io.sockets.sockets.get(partnerSocketId)?.join(roomId);

    // Notifica entrambi gli utenti con evento corretto
    socket.emit('match-found', {
      roomId,
      partnerId: partnerUser.id,
      partnerEmail: partnerUser.email
    });

    io.to(partnerSocketId).emit('match-found', {
      roomId,
      partnerId: user.id,
      partnerEmail: user.email
    });

    // Decrementa i crediti
    user.credits -= 1;
    partnerUser.credits -= 1;
  });

  // Compatibilità con il vecchio evento
  socket.on('request-videochat', (data) => {
    socket.emit('find-match', data);
  });

  // Segnali WebRTC - eventi corretti
  socket.on('webrtc-offer', (data) => {
    console.log('Ricevuto offer per room:', data.roomId);
    socket.to(data.roomId).emit('offer', {
      offer: data.offer,
      from: socket.id
    });
  });

  socket.on('webrtc-answer', (data) => {
    console.log('Ricevuto answer per room:', data.roomId);
    socket.to(data.roomId).emit('answer', {
      answer: data.answer,
      from: socket.id
    });
  });

  socket.on('webrtc-ice-candidate', (data) => {
    console.log('Ricevuto ICE candidate per room:', data.roomId);
    socket.to(data.roomId).emit('ice-candidate', {
      candidate: data.candidate,
      from: socket.id
    });
  });

  // Eventi WebRTC alternativi per compatibilità
  socket.on('offer', (data) => {
    socket.to(data.roomId).emit('offer', data);
  });

  socket.on('answer', (data) => {
    socket.to(data.roomId).emit('answer', data);
  });

  socket.on('ice-candidate', (data) => {
    socket.to(data.roomId).emit('ice-candidate', data);
  });

  // Messaggi di chat
  socket.on('chat-message', (data) => {
    socket.to(data.roomId).emit('chat-message', {
      message: data.message,
      from: socket.userId,
      timestamp: new Date()
    });
  });

  // Disconnessione
  socket.on('disconnect', () => {
    console.log('Utente disconnesso:', socket.id);
    
    const user = activeUsers.get(socket.id);
    if (user) {
      user.isOnline = false;
      activeUsers.delete(socket.id);
      
      // Notifica agli altri utenti
      socket.broadcast.emit('user-offline', {
        userId: user.id,
        email: user.email
      });
    }

    // Rimuovi dalle stanze di chat
    for (const [roomId, room] of chatRooms.entries()) {
      if (room.users.includes(socket.id)) {
        const otherUser = room.users.find(id => id !== socket.id);
        if (otherUser) {
          io.to(otherUser).emit('partner-disconnected');
        }
        chatRooms.delete(roomId);
      }
    }
  });
});

// Servire l'applicazione
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../public', 'index.html'));
});

// Avvio del server
const PORT = process.env.PORT || 3001;
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 VideoChat Couple server in esecuzione sulla porta ${PORT}`);
  console.log(`🌐 Accesso esterno: https://videochatcouple.com:${PORT}`);
});

module.exports = app;

