<!DOCTYPE html>
<html lang="it">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Registrati - VideoChatCouple</title>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;800&family=Orbitron:wght@400;700;900&family=Space+Grotesk:wght@400;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/assets/styles.css">
  <style>
    body {
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      font-family: 'Space Grotesk', sans-serif;
      padding-top: 80px;
      margin: 0;
    }
    .site-header {
      position: fixed;
      top: 0; left: 0; right: 0;
      z-index: 1000;
      background: rgba(0,0,0,0.7);
      backdrop-filter: blur(10px);
      padding: 15px 0;
    }
    .site-title {
      font-family: 'Orbitron', sans-serif;
      font-size: 38px;
      font-weight: 800;
      background: linear-gradient(45deg, #fff, #e0e0e0);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      text-align: center;
      margin: 0;
    }
    .register-container {
      background: rgba(255,255,255,0.09);
      border: 1px solid rgba(255,255,255,0.12);
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.2);
      max-width: 420px;
      margin: 120px auto 40px;
      padding: 40px 30px 30px 30px;
      display: flex;
      flex-direction: column;
      align-items: center;
      animation: fadeSlideIn 0.7s;
    }
    @keyframes fadeSlideIn {
      from { opacity: 0; transform: translateY(30px);}
      to { opacity: 1; transform: translateY(0);}
    }
    .register-container h2 {
      font-family: 'Poppins', sans-serif;
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
      background: linear-gradient(45deg, #00c6ff, #0072ff);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
    .form-group {
      width: 100%;
      margin-bottom: 18px;
      text-align: left;
    }
    .form-group label {
      font-family: 'Space Grotesk', sans-serif;
      font-size: 1.1rem;
      font-weight: 600;
      color: rgba(255,255,255,0.9);
      margin-bottom: 0.5rem;
      display: block;
    }
    .form-group input {
      width: 100%;
      font-size: 1rem;
      padding: 1rem;
      background: rgba(255,255,255,0.08);
      border: 1px solid rgba(255,255,255,0.1);
      border-radius: 12px;
      color: white;
      transition: all 0.3s;
      margin-top: 5px;
    }
    .form-group input:focus {
      background: rgba(255,255,255,0.12);
      border-color: #00c6ff;
      box-shadow: 0 0 15px rgba(0,198,255,0.1);
      outline: none;
    }
    .primary-button {
      background: linear-gradient(45deg, #00c6ff, #0072ff);
      font-family: 'Poppins', sans-serif;
      font-weight: 600;
      font-size: 1.1rem;
      padding: 1rem 2rem;
      border-radius: 8px;
      border: none;
      color: white;
      width: 100%;
      margin-top: 10px;
      cursor: pointer;
      transition: all 0.3s;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
    .primary-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    .register-link {
      margin-top: 18px;
      color: #00c6ff;
      text-align: center;
      cursor: pointer;
      font-size: 1rem;
      text-decoration: underline;
    }
    .register-success {
      color: #2ecc71;
      margin-top: 12px;
      text-align: center;
    }
    .register-error {
      color: #ff7675;
      margin-top: 12px;
      text-align: center;
    }
  </style>
</head>
<body>
  <header class="site-header">
    <h1 class="site-title">VideoChatCouple</h1>
  </header>
  <div class="register-container">
    <h2>Crea il tuo account</h2>
    <form id="register-form" autocomplete="off">
      <div class="form-group">
        <label for="reg-email">Email</label>
        <input type="email" id="reg-email" required placeholder="La tua email">
      </div>
      <div class="form-group">
        <label for="reg-password">Password</label>
        <input type="password" id="reg-password" required minlength="6" placeholder="Minimo 6 caratteri">
      </div>
      <div class="form-group">
        <label for="reg-confirm">Conferma Password</label>
        <input type="password" id="reg-confirm" required minlength="6" placeholder="Ripeti la password">
      </div>
      <div class="form-group">
        <label>
          <input type="checkbox" id="reg-age" required>
          Confermo di avere almeno 18 anni e di accettare i termini di utilizzo.
        </label>
      </div>
      <button class="primary-button" id="register-btn" type="submit" disabled>Registrati</button>
      <div id="register-feedback"></div>
    </form>
    <div class="register-link" onclick="window.location.href='/index.html'">
      Hai già un account? Accedi
    </div>
  </div>
  <script>
    // Validazione form registrazione
    const regEmail = document.getElementById('reg-email');
    const regPassword = document.getElementById('reg-password');
    const regConfirm = document.getElementById('reg-confirm');
    const regAge = document.getElementById('reg-age');
    const regBtn = document.getElementById('register-btn');
    const regForm = document.getElementById('register-form');
    const regFeedback = document.getElementById('register-feedback');

    function validateRegisterForm() {
      regBtn.disabled = !(
        regEmail.value.trim() &&
        regPassword.value.length >= 6 &&
        regPassword.value === regConfirm.value &&
        regAge.checked
      );
    }
    regEmail.addEventListener('input', validateRegisterForm);
    regPassword.addEventListener('input', validateRegisterForm);
    regConfirm.addEventListener('input', validateRegisterForm);
    regAge.addEventListener('change', validateRegisterForm);

    regForm.addEventListener('submit', async function(e) {
      e.preventDefault();
      regBtn.disabled = true;
      regBtn.textContent = "Registrazione...";
      regFeedback.textContent = "";

      try {
        const response = await fetch('/api/register', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            email: regEmail.value,
            password: regPassword.value,
            ageVerified: regAge.checked
          })
        });

        const data = await response.json();

        if (response.ok && data.success) {
          regFeedback.textContent = "Registrazione avvenuta! Ora puoi accedere.";
          regFeedback.className = "register-success";
          regBtn.textContent = "Registrato!";
          setTimeout(() => window.location.href = "/index.html", 1500);
        } else {
          regFeedback.textContent = data.error || "Errore durante la registrazione";
          regFeedback.className = "register-error";
          regBtn.disabled = false;
          regBtn.textContent = "Registrati";
        }
      } catch (err) {
        console.error('Registration error:', err);
        regFeedback.textContent = "Errore di rete: " + (err.message || 'Impossibile connettersi al server');
        regFeedback.className = "register-error";
        regBtn.disabled = false;
        regBtn.textContent = "Registrati";
      }
    });
  </script>
</body>
</html>
