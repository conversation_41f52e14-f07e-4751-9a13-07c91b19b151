/**
 * VideoChat Couple - JavaScript per Login
 * Versione: SEMPLIFICATA - Solo gestione login
 */

console.log('🚀 Caricamento script login semplificato...');

// Unico script per la gestione dell'autenticazione
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded, initializing auth handler...');

  // Singolo riferimento agli elementi DOM
  const elements = {
    form: document.getElementById('login-form'),
    loginButton: document.getElementById('login-button'),
    registerLink: document.getElementById('register-link'),
    ageCheckbox: document.getElementById('age-verification'),
    emailInput: document.getElementById('email'),
    passwordInput: document.getElementById('password')
  };

  // Debug elementi
  console.log('Elements found:', { 
      loginButton: !!elements.loginButton,
      registerLink: !!elements.registerLink,
      ageVerification: !!elements.ageCheckbox,
      emailInput: !!elements.emailInput,
      passwordInput: !!elements.passwordInput
  });

  // Funzione per validare il form di login
  function validateLoginForm() {
    elements.loginButton.disabled = !(
      elements.emailInput.value.trim() &&
      elements.passwordInput.value.trim() &&
      elements.ageCheckbox.checked
    );
  }

  // Event listener per la validazione del form
  elements.emailInput.addEventListener('input', validateLoginForm);
  elements.passwordInput.addEventListener('input', validateLoginForm);
  elements.ageCheckbox.addEventListener('change', validateLoginForm);

  // Gestione invio form
  elements.form.addEventListener('submit', async function(e) {
    e.preventDefault();
    elements.loginButton.disabled = true;
    elements.loginButton.textContent = "Accesso in corso...";
    const errorDiv = document.getElementById('login-error');
    if (errorDiv) errorDiv.remove();

    try {
      // Usa URL relativo invece di hardcoded
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          email: elements.emailInput.value,
          password: elements.passwordInput.value
        })
      });
      const data = await response.json();
      if (response.ok && data.token && data.success) {
        localStorage.setItem('authToken', data.token);
        localStorage.setItem('userEmail', data.user.email);
        localStorage.setItem('userCredits', data.user.credits);
        console.log('Login successful, redirecting to dashboard...');
        window.location.href = "/dashboard.html";
      } else {
        showError(data.error || data.message || 'Errore di login');
      }
    } catch (err) {
      console.error('Login error:', err);
      showError('Errore di rete: ' + (err.message || 'Impossibile connettersi al server'));
    }
    elements.loginButton.disabled = false;
    elements.loginButton.textContent = "Accedi";

    // Funzione per mostrare errori
    function showError(msg) {
      const err = document.createElement('div');
      err.id = 'login-error';
      err.style.color = '#ff7675';
      err.style.marginTop = '10px';
      err.textContent = msg;
      elements.form.appendChild(err);
    }
  });

  // Gestione click registrati
  elements.registerLink.addEventListener('click', function() {
    window.location.href = '/register.html';
  });
});

