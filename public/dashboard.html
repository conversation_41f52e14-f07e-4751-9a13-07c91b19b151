<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VideoChat Dashboard</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-family: 'Arial', sans-serif;
        }

        .dashboard {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .video-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        video {
            width: 100%;
            background: #000;
            border-radius: 8px;
        }

        .controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            background: rgba(0,0,0,0.5);
            padding: 15px;
            border-radius: 50px;
        }

        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-size: 20px;
        }

        .preferences {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .search-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }

        /* Nuovo stile per l'overlay dei permessi */
        #permission-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.9);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: white;
        }

        #request-permissions {
            padding: 15px 30px;
            background: #4CAF50;
            border: none;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
        }
    </style>
    <script src="/static/js/webrtc-config.js"></script>
    <script src="/videochat-handler.js"></script>
    <script>
        // Initialize required globals
        let localStream = null;
        let peerConnection = null;

        // WebRTC configuration
        const webrtcConfig = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' }
            ]
        };

        // Simple WebRTC check
        const isWebRTCSupported = () => {
            return !!(navigator.mediaDevices &&
                navigator.mediaDevices.getUserMedia &&
                window.RTCPeerConnection);
        };

        document.addEventListener('DOMContentLoaded', async () => {
            if (!isWebRTCSupported()) {
                alert('Il tuo browser non supporta WebRTC. Prova con Chrome o Firefox.');
                return;
            }

            try {
                console.log('Requesting webcam access...');
                localStream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 1280, height: 720 },
                    audio: true
                });
                
                const localVideo = document.getElementById('localVideo');
                localVideo.srcObject = localStream;
                document.getElementById('permission-overlay').style.display = 'none';
                
                // Setup control buttons
                document.getElementById('toggleVideo').onclick = () => {
                    if (window.videoChatHandler) {
                        const enabled = window.videoChatHandler.toggleVideo();
                        document.getElementById('toggleVideo').textContent = enabled ? '🎥' : '🚫';
                    } else {
                        const videoTrack = localStream.getVideoTracks()[0];
                        videoTrack.enabled = !videoTrack.enabled;
                        document.getElementById('toggleVideo').textContent =
                            videoTrack.enabled ? '🎥' : '🚫';
                    }
                };

                document.getElementById('toggleAudio').onclick = () => {
                    if (window.videoChatHandler) {
                        const enabled = window.videoChatHandler.toggleAudio();
                        document.getElementById('toggleAudio').textContent = enabled ? '🎤' : '🔇';
                    } else {
                        const audioTrack = localStream.getAudioTracks()[0];
                        audioTrack.enabled = !audioTrack.enabled;
                        document.getElementById('toggleAudio').textContent =
                            audioTrack.enabled ? '🎤' : '🔇';
                    }
                };

                document.getElementById('startSearch').onclick = () => {
                    const preference = document.querySelector('.gender-btn.selected')?.dataset.gender || 'any';
                    if (window.videoChatHandler) {
                        window.videoChatHandler.startSearch(preference);
                    } else {
                        console.error('VideoChatHandler not available');
                    }
                };

                document.getElementById('endCall').onclick = () => {
                    if (window.videoChatHandler) {
                        window.videoChatHandler.endCall();
                    }
                };

                document.getElementById('nextUser').onclick = () => {
                    if (window.videoChatHandler) {
                        window.videoChatHandler.endCall();
                        setTimeout(() => {
                            const preference = document.querySelector('.gender-btn.selected')?.dataset.gender || 'any';
                            window.videoChatHandler.startSearch(preference);
                        }, 1000);
                    }
                };

            } catch (err) {
                console.error('MediaDevice error:', err);
                alert('Errore accesso webcam: ' + err.message);
            }
        });

        // Verifica token all'avvio
        document.addEventListener('DOMContentLoaded', () => {
            const token = localStorage.getItem('authToken');
            if (!token) {
                window.location.href = '/';
                return;
            }
            // Usa il token per autenticare le chiamate/fetch/socket
            // Inizializza videochat
            initializeWebRTC();
        });

        // Verifica autenticazione
        const token = localStorage.getItem('authToken');
        const userEmail = localStorage.getItem('userEmail');
        if (!token || !userEmail) {
            window.location.href = '/';
            return;
        }

        // Il socket viene gestito dal VideoChatHandler
        console.log('Dashboard loaded for user:', userEmail);

        document.getElementById('request-permissions').onclick = async () => {
            try {
                localStream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 1280, height: 720 },
                    audio: true
                });
                document.getElementById('localVideo').srcObject = localStream;
                document.getElementById('permission-overlay').style.display = 'none';
            } catch (err) {
                alert('Devi consentire webcam e microfono per usare la videochat');
            }
        };

        document.querySelectorAll('.gender-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.gender-btn').forEach(b => b.classList.remove('selected'));
                btn.classList.add('selected');
            });
        });
    </script>
</head>
<body>
    <div class="dashboard">
        <!-- Aggiungi un overlay per la richiesta permessi -->
        <div id="permission-overlay" style="display: flex;">
            <h2>Permessi Richiesti</h2>
            <p>Per utilizzare la videochat, devi consentire l'accesso a webcam e microfono</p>
            <button id="request-permissions">Consenti Accesso</button>
        </div>

        <div class="preferences">
            <h3>Preferenze Ricerca</h3>
            <button class="gender-btn" data-gender="male">Uomo</button>
            <button class="gender-btn" data-gender="female">Donna</button>
            <button class="gender-btn" data-gender="all">Tutti</button>
            <button id="startSearch" class="search-btn">Inizia Ricerca</button>
        </div>

        <div class="video-container">
            <video id="remoteVideo" autoplay playsinline></video>
            <video id="localVideo" autoplay playsinline muted></video>
        </div>

        <div class="controls">
            <button class="control-btn" id="toggleVideo">🎥</button>
            <button class="control-btn" id="toggleAudio">🎤</button>
            <button class="control-btn" id="nextUser">⏭️</button>
            <button class="control-btn" id="endCall">❌</button>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
</body>
</html>