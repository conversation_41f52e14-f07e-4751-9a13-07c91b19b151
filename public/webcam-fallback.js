/**
 * Fallback per webcam su connessioni HTTP non sicure
 */

class WebcamFallback {
    constructor() {
        this.isSecureContext = window.isSecureContext || location.protocol === 'https:';
        this.localStream = null;
    }

    async requestPermissions() {
        if (!this.isSecureContext) {
            // Su HTTP, mostra istruzioni per abilitare la webcam
            this.showHTTPInstructions();
            return null;
        }

        try {
            this.localStream = await navigator.mediaDevices.getUserMedia({
                video: { width: 1280, height: 720 },
                audio: true
            });
            return this.localStream;
        } catch (err) {
            console.error('Errore accesso webcam:', err);
            this.showPermissionError(err);
            throw err;
        }
    }

    showHTTPInstructions() {
        const overlay = document.createElement('div');
        overlay.id = 'http-instructions-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0; left: 0; right: 0; bottom: 0;
            background: rgba(0,0,0,0.9);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-family: Arial, sans-serif;
        `;

        overlay.innerHTML = `
            <div style="max-width: 600px; padding: 40px; text-align: center; background: rgba(255,255,255,0.1); border-radius: 15px;">
                <h2 style="color: #ff6b6b; margin-bottom: 20px;">🔒 Connessione Non Sicura</h2>
                <p style="font-size: 18px; margin-bottom: 30px;">
                    Per utilizzare la webcam, devi accedere al sito tramite HTTPS.
                </p>
                
                <div style="background: rgba(0,0,0,0.3); padding: 20px; border-radius: 10px; margin: 20px 0;">
                    <h3 style="color: #4ecdc4;">🚀 Soluzioni:</h3>
                    <div style="text-align: left; margin: 15px 0;">
                        <p><strong>1. Usa HTTPS:</strong></p>
                        <a href="https://localhost:3002" style="color: #4ecdc4; text-decoration: none; font-weight: bold;">
                            https://localhost:3002
                        </a>
                        <p style="font-size: 14px; color: #ccc; margin-top: 5px;">
                            (Accetta il certificato self-signed quando richiesto)
                        </p>
                    </div>
                    
                    <div style="text-align: left; margin: 15px 0;">
                        <p><strong>2. Abilita webcam per localhost:</strong></p>
                        <p style="font-size: 14px; color: #ccc;">
                            Chrome: chrome://flags/#unsafely-treat-insecure-origin-as-secure<br>
                            Aggiungi: http://localhost:3002
                        </p>
                    </div>
                </div>
                
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="background: #4ecdc4; color: white; border: none; padding: 15px 30px; border-radius: 8px; cursor: pointer; font-size: 16px;">
                    Ho Capito
                </button>
                
                <div style="margin-top: 20px;">
                    <button onclick="window.location.href='https://localhost:3002'" 
                            style="background: #ff6b6b; color: white; border: none; padding: 15px 30px; border-radius: 8px; cursor: pointer; font-size: 16px; margin: 0 10px;">
                        Vai a HTTPS
                    </button>
                    <button onclick="window.location.reload()" 
                            style="background: #95a5a6; color: white; border: none; padding: 15px 30px; border-radius: 8px; cursor: pointer; font-size: 16px; margin: 0 10px;">
                        Riprova
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(overlay);
    }

    showPermissionError(error) {
        let message = 'Errore sconosciuto';
        let solution = '';

        switch (error.name) {
            case 'NotAllowedError':
                message = 'Accesso alla webcam negato';
                solution = 'Clicca sull\'icona della webcam nella barra degli indirizzi e consenti l\'accesso.';
                break;
            case 'NotFoundError':
                message = 'Webcam non trovata';
                solution = 'Verifica che la webcam sia collegata e funzionante.';
                break;
            case 'NotReadableError':
                message = 'Webcam in uso da un\'altra applicazione';
                solution = 'Chiudi altre applicazioni che potrebbero usare la webcam.';
                break;
            case 'OverconstrainedError':
                message = 'Impostazioni webcam non supportate';
                solution = 'La tua webcam non supporta le impostazioni richieste.';
                break;
            case 'SecurityError':
                message = 'Errore di sicurezza';
                solution = 'Usa HTTPS o abilita la webcam per questo sito.';
                break;
        }

        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px; right: 20px;
            background: #ff6b6b;
            color: white;
            padding: 20px;
            border-radius: 10px;
            max-width: 400px;
            z-index: 9999;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        `;

        errorDiv.innerHTML = `
            <h4 style="margin: 0 0 10px 0;">❌ ${message}</h4>
            <p style="margin: 0 0 15px 0; font-size: 14px;">${solution}</p>
            <button onclick="this.parentElement.remove()" 
                    style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">
                Chiudi
            </button>
        `;

        document.body.appendChild(errorDiv);

        // Rimuovi automaticamente dopo 10 secondi
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 10000);
    }

    // Verifica se WebRTC è supportato
    static isWebRTCSupported() {
        return !!(navigator.mediaDevices &&
            navigator.mediaDevices.getUserMedia &&
            window.RTCPeerConnection);
    }

    // Verifica se siamo in un contesto sicuro
    static isSecureContext() {
        return window.isSecureContext || location.protocol === 'https:';
    }
}

// Esporta per uso globale
window.WebcamFallback = WebcamFallback;
