<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend - VideoChat Couple</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .success { color: green; }
        .error { color: red; }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Frontend VideoChat Couple</h1>
    
    <div class="test-section">
        <h2>1. Test Registrazione</h2>
        <input type="email" id="reg-email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="reg-password" placeholder="Password" value="password123">
        <button onclick="testRegister()">Test Registrazione</button>
        <div id="reg-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Login</h2>
        <input type="email" id="login-email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="login-password" placeholder="Password" value="password123">
        <button onclick="testLogin()">Test Login</button>
        <div id="login-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. Test Socket.IO</h2>
        <button onclick="testSocket()">Test Connessione Socket</button>
        <div id="socket-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. Test WebRTC</h2>
        <button onclick="testWebRTC()">Test Supporto WebRTC</button>
        <div id="webrtc-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>5. Stato Autenticazione</h2>
        <button onclick="checkAuth()">Verifica Token</button>
        <button onclick="clearAuth()">Cancella Token</button>
        <div id="auth-result" class="result"></div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        // Test Registrazione
        async function testRegister() {
            const result = document.getElementById('reg-result');
            const email = document.getElementById('reg-email').value;
            const password = document.getElementById('reg-password').value;
            
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        ageVerified: true
                    })
                });
                
                const data = await response.json();
                result.className = 'result ' + (response.ok ? 'success' : 'error');
                result.textContent = `Status: ${response.status}\n${JSON.stringify(data, null, 2)}`;
            } catch (err) {
                result.className = 'result error';
                result.textContent = 'Errore: ' + err.message;
            }
        }

        // Test Login
        async function testLogin() {
            const result = document.getElementById('login-result');
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                result.className = 'result ' + (response.ok ? 'success' : 'error');
                result.textContent = `Status: ${response.status}\n${JSON.stringify(data, null, 2)}`;
                
                if (response.ok && data.token) {
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('userEmail', data.user.email);
                    result.textContent += '\n\n✅ Token salvato in localStorage';
                }
            } catch (err) {
                result.className = 'result error';
                result.textContent = 'Errore: ' + err.message;
            }
        }

        // Test Socket.IO
        function testSocket() {
            const result = document.getElementById('socket-result');
            result.textContent = 'Connessione in corso...';
            
            const socket = io('/', {
                transports: ['websocket'],
                upgrade: false
            });
            
            socket.on('connect', () => {
                result.className = 'result success';
                result.textContent = '✅ Socket.IO connesso!\nSocket ID: ' + socket.id;
                
                const token = localStorage.getItem('authToken');
                if (token) {
                    socket.emit('authenticate', token);
                    result.textContent += '\n🔐 Token inviato per autenticazione';
                }
            });
            
            socket.on('authenticated', (data) => {
                result.textContent += '\n✅ Autenticazione socket riuscita: ' + JSON.stringify(data);
            });
            
            socket.on('auth-error', (error) => {
                result.textContent += '\n❌ Errore autenticazione socket: ' + error;
            });
            
            socket.on('connect_error', (err) => {
                result.className = 'result error';
                result.textContent = '❌ Errore connessione Socket.IO: ' + err.message;
            });
            
            setTimeout(() => {
                socket.disconnect();
            }, 5000);
        }

        // Test WebRTC
        function testWebRTC() {
            const result = document.getElementById('webrtc-result');
            
            const checks = [];
            
            // Verifica supporto getUserMedia
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                checks.push('✅ getUserMedia supportato');
            } else {
                checks.push('❌ getUserMedia NON supportato');
            }
            
            // Verifica supporto RTCPeerConnection
            if (window.RTCPeerConnection) {
                checks.push('✅ RTCPeerConnection supportato');
            } else {
                checks.push('❌ RTCPeerConnection NON supportato');
            }
            
            // Verifica supporto WebRTC
            const isWebRTCSupported = !!(navigator.mediaDevices &&
                navigator.mediaDevices.getUserMedia &&
                window.RTCPeerConnection);
            
            if (isWebRTCSupported) {
                checks.push('✅ WebRTC completamente supportato');
                result.className = 'result success';
            } else {
                checks.push('❌ WebRTC NON completamente supportato');
                result.className = 'result error';
            }
            
            result.textContent = checks.join('\n');
        }

        // Verifica autenticazione
        function checkAuth() {
            const result = document.getElementById('auth-result');
            const token = localStorage.getItem('authToken');
            const email = localStorage.getItem('userEmail');
            
            if (token && email) {
                result.className = 'result success';
                result.textContent = `✅ Autenticato come: ${email}\nToken: ${token.substring(0, 50)}...`;
            } else {
                result.className = 'result error';
                result.textContent = '❌ Non autenticato';
            }
        }

        // Cancella autenticazione
        function clearAuth() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('userEmail');
            localStorage.removeItem('userCredits');
            document.getElementById('auth-result').textContent = '🗑️ Token cancellati';
        }

        // Carica stato iniziale
        window.onload = function() {
            checkAuth();
        };
    </script>
</body>
</html>
