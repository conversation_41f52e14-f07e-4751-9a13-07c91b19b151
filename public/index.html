<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VideoChat Couple - Login</title>
    <!-- Google Fonts aggiornati per stile più accattivante -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;800&family=Orbitron:wght@400;700;900&family=Space+Grotesk:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/styles.css">

    <style>
        /* Layout principale aggiornato */
        .main-container {
            display: grid;
            grid-template-columns: 1.2fr 0.8fr; /* Ridimensiona le colonne */
            gap: 40px;
            max-width: 1400px;
            margin: 100px auto 40px;
            padding: 0 30px;
            align-items: center; /* Allinea al centro verticalmente */
        }

        /* Stile titolo migliorato */
        .site-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            padding: 15px 0;
        }

        .site-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 42px;
            font-weight: 800;
            background: linear-gradient(45deg, #fff, #e0e0e0);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 3px;
            text-align: center;
            margin: 0;
        }

        /* Termini di servizio orizzontali */
        .terms-service {
            grid-column: 1 / -1;  /* Occupa tutta la larghezza */
            margin-top: 40px;
        }

        .terms-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .terms-item {
            background: rgba(52, 152, 219, 0.1);
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #3498db;
            backdrop-filter: blur(8px);
            transition: transform 0.3s ease;
        }

        .terms-item:hover {
            transform: translateY(-5px);
        }

        /* Layout a due colonne */
        .main-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            max-width: 1400px;
            margin: 100px auto 40px;
            padding: 0 30px;
        }

        /* Stile migliorato per la descrizione */
        .site-description {
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 40px;
            margin: 0;
            height: auto;
            min-height: 680px; /* Altezza fissa per entrambi i contenitori */
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            align-self: flex-start; /* Forza allineamento in alto */
        }

        .site-description:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3),
                        inset 0 1px 2px rgba(255, 255, 255, 0.2);
        }

        .site-description h2 {
            font-size: 2.8rem;
            background: linear-gradient(135deg, #00c6ff, #0072ff, #764ba2);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .site-description h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, #00c6ff, #0072ff);
            border-radius: 2px;
        }

        /* Form di login migliorato */
        .auth-container {
            display: flex;
            flex-direction: column;
            width: 100%;
            max-width: 400px; /* Ridotto da 500px */
            min-height: 520px; /* Ridotto da 600px */
            margin-top: 50px; /* Sposta verso il basso */
            padding: 30px; /* Ridotto da 40px */
            background: rgba(255, 255, 255, 0.09);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 20px;
            animation: fadeSlideIn 0.6s ease-out;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        @keyframes fadeSlideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-group {
            margin-bottom: 15px; /* Ridotto da 20px */
        }

        .form-group input {
            background: rgba(255, 255, 255, 0.07);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px; /* Ridotto da 15px */
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(0, 198, 255, 0.5);
            box-shadow: 0 0 20px rgba(0, 198, 255, 0.2);
        }

        /* Bottoni più attraenti */
        .primary-button {
            background: linear-gradient(45deg, #00c6ff, #0072ff);
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            letter-spacing: 1px;
            padding: 16px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .primary-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
            );
            transition: 0.5s;
        }

        .primary-button:hover::before {
            left: 100%;
        }

        /* Stili base aggiornati */
        body {
            min-height: 100vh;
            padding-top: 80px; /* Spazio per header fisso */
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-family: 'Space Grotesk', sans-serif;
        }

        .login-container {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            color: white;
            text-align: center;
            max-width: 400px;
            width: 90%;
        }
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        input {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            background: rgba(255,255,255,0.2);
            color: white;
            margin-top: 5px;
        }
        button {
            background: #4CAF50;
            border: none;
            padding: 12px 30px;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background: #45a049;
        }

        /* Stili per lo sfondo 3D e l'indicatore di utenti online */
        #canvas-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        .online-users-badge {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.2);
            padding: 10px;
            border-radius: 5px;
            color: white;
            display: flex;
            align-items: center;
        }
        .online-indicator {
            width: 10px;
            height: 10px;
            background: #4CAF50;
            border-radius: 50%;
            margin-right: 5px;
        }

        /* Aggiungi stili per il contenuto del sito */
        .site-description {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .site-description h2 {
            font-family: 'Poppins', sans-serif;
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(45deg, #00c6ff, #0072ff);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 1.5rem;
            letter-spacing: -0.5px;
        }

        .site-description p {
            font-family: 'Space Grotesk', sans-serif;
            font-size: 1.2rem;
            line-height: 1.8;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 2rem;
        }

        .site-description h3 {
            font-family: 'Poppins', sans-serif;
            font-size: 1.8rem;
            font-weight: 600;
            color: #fff;
            margin: 2rem 0 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* Stili per le liste */
        .privacy-features li, .site-description ul li {
            font-family: 'Space Grotesk', sans-serif;
            font-size: 1.1rem;
            line-height: 1.6;
            letter-spacing: 0.2px;
            padding: 0.8rem 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            margin-bottom: 0.8rem;
            transition: all 0.3s ease;
        }

        .privacy-features li:hover, .site-description ul li:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        /* Form di autenticazione aggiornato */
        .auth-container {
            font-family: 'Space Grotesk', sans-serif;
            display: flex;
            flex-direction: column;
            width: 100%;
            max-width: 400px; /* Ridotto da 500px */
            min-height: 520px; /* Ridotto da 600px */
            margin-top: 50px; /* Sposta verso il basso */
            padding: 30px; /* Ridotto da 40px */
            background: rgba(255, 255, 255, 0.09);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .auth-container h2 {
            font-family: 'Poppins', sans-serif;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            background: linear-gradient(45deg, #00c6ff, #0072ff);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .form-group label {
            font-family: 'Space Grotesk', sans-serif;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: rgba(255, 255, 255, 0.9);
        }

        .form-group input {
            font-family: 'Space Grotesk', sans-serif;
            font-size: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 15px rgba(0, 198, 255, 0.1);
        }

        /* Bottoni aggiornati */
        .primary-button, .register-button {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 1.1rem;
            padding: 1rem 2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .primary-button {
            background: linear-gradient(45deg, #00c6ff, #0072ff);
            box-shadow: 0 4px 15px rgba(0, 198, 255, 0.2);
        }

        .primary-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 198, 255, 0.3);
        }

        /* Footer aggiornato */
        .footer-content {
            font-family: 'Space Grotesk', sans-serif;
        }

        .footer-links a {
            font-family: 'Poppins', sans-serif;
            font-weight: 500;
            font-size: 1rem;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        /* Stili per i termini di servizio */
        .terms-service {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .terms-service h3 {
            color: #fff;
            font-family: 'Poppins', sans-serif;
            font-size: 24px;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .terms-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .terms-item {
            background: rgba(52, 152, 219, 0.1);
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #3498db;
            backdrop-filter: blur(8px);
            transition: transform 0.3s ease;
        }

        .terms-item:hover {
            transform: translateY(-5px);
        }

        /* Fix scroll behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Fix altezze contenuti interni */
        .site-description > *, .auth-container > * {
            margin-bottom: 20px;
        }

        .site-description > *:last-child, .auth-container > *:last-child {
            margin-bottom: 0;
        }

        /* Rendi i termini sempre in fondo */
        .terms-service {
            grid-column: 1 / -1;
            margin-top: 40px;
        }

        /* Fix responsive */
        @media (max-width: 1024px) {
            .main-container {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .site-description, .auth-container {
                height: auto;
            }
        }
    </style>
</head>
<body>
  <!-- Contenitore per lo sfondo 3D -->
  <div id="canvas-container"></div>

  <!-- Indicatore di utenti online -->
  <div class="online-users-badge">
    <div class="online-indicator"></div>
    <span id="online-count">1,253</span> <span id="online-users-text">utenti online</span>
  </div>

  <!-- Header con il nome del sito -->
  <header class="site-header">
    <h1 class="site-title">VideoChat Couple</h1>
    <div class="title-underline"></div>
  </header>

  <!-- Contenitore principale -->
  <div class="main-container">
    <!-- Descrizione del sito -->
    <div class="site-description">
      <h2 class="welcome-title">Benvenuto su VideoChatCouple</h2>
      <p>VideoChat Couple è la piattaforma che ti permette di connetterti con persone in tutto il mondo tramite video chat e messaggistica in tempo reale.</p>
      
      <h3>Regole di utilizzo:</h3>
      <ul>
          <li>🔞 Devi avere almeno 18 anni per utilizzare questo servizio.</li>
          <li>🤝 Rispetta gli altri utenti e non utilizzare linguaggio offensivo.</li>
          <li>👔 Non condividere contenuti inappropriati o illegali.</li>
          <li>🔐 Non registrare o salvare le conversazioni senza il consenso.</li>
          <li>🛡️ Segnala comportamenti inappropriati.</li>
      </ul>

      <h3>La tua Privacy è Importante</h3>
      <ul class="privacy-features">
          <li>🔒 Chat criptate end-to-end</li>
          <li>🎥 Nessuna registrazione delle videochiamate</li>
          <li>🚫 Nessun dato personale condiviso</li>
          <li>📍 Geolocalizzazione disattivata per default</li>
          <li>🔐 Connessione HTTPS sicura</li>
      </ul>
    </div>

    <!-- Form di autenticazione tolto da auth-container animated-text -->
    <div class="auth-container" id="auth-container" style="animation-delay: 1.3s">
      <h2 style="font-family: 'Montserrat', sans-serif; font-weight: 700;">Accedi</h2>
      <p style="text-align: center; margin-bottom: 20px; font-family: 'Raleway', sans-serif;">
        <span>Accedi per iniziare a connetterti con persone da tutto il mondo.</span>
      </p>
      <div class="free-credits-banner">
        <div class="banner-content">
          <h3 style="font-family: 'Montserrat', sans-serif;">10 CREDITI GRATUITI</h3>
          <p style="font-family: 'Raleway', sans-serif;">Registrati oggi e ricevi subito 10 crediti gratuiti per iniziare!</p>
        </div>
      </div>
      <form id="login-form" autocomplete="on">
        <div class="form-group">
          <label for="email">Email</label>
          <input type="email" id="email" name="email" required autocomplete="username">
        </div>
        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" id="password" name="password" required autocomplete="current-password">
        </div>
        <div class="age-verification">
          <label>
            <input type="checkbox" id="age-verification" required>
            Confermo di avere almeno 18 anni e di accettare i termini di utilizzo.
          </label>
        </div>
        <button class="primary-button" id="login-button" type="submit" disabled>Accedi</button>
        <button class="register-button" id="register-link" type="button">Non hai un account? Registrati</button>
      </form>
    </div>
  </div>
  <!-- Termini di servizio spostati in fondo e orizzontali -->
  <div class="terms-service">
      <h3>Termini di Servizio</h3>
      <div class="terms-grid">
          <div class="terms-item">
              <h4>🔒 Privacy e Sicurezza</h4>
              <p>Tutte le conversazioni sono private e criptate. Non salviamo alcun dato delle videochiamate.</p>
          </div>
          <div class="terms-item">
              <h4>💳 Sistema Crediti</h4>
              <p>10 crediti gratuiti all'iscrizione. Ogni videochiamata costa 1 credito per minuto.</p>
          </div>
          <div class="terms-item">
              <h4>🔞 Verifica Età</h4>
              <p>Accesso consentito solo ai maggiorenni. Richiesta verifica dell'età tramite documento.</p>
          </div>
          <div class="terms-item">
              <h4>📞 Assistenza Clienti</h4>
              <p>Supporto disponibile 24/7 tramite chat e email.</p>
          </div>
      </div>
  </div>
  <!-- Footer e altri elementi -->
  <!-- Riordina gli script nel corretto ordine di caricamento -->
  <!-- Script di monitoraggio e gestione errori (caricati per primi) -->
  <!-- <script src="/error-handler.js"></script> -->
  <!-- <script src="/performance-monitor.js"></script> -->
  <script src="/socket.io/socket.io.js"></script>
  <script src="/assets/cubes-animation.js"></script>
  <script src="/auth-handler.js"></script>
  <script>
    // Gestione reale del pulsante "Registrati"
    document.getElementById('register-link').addEventListener('click', function(e) {
      window.location.href = '/register.html';
    });

    // UX: Abilita/disabilita il bottone "Accedi" solo se tutti i campi sono validi
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const ageCheckbox = document.getElementById('age-verification');
    const loginButton = document.getElementById('login-button');
    const loginForm = document.getElementById('login-form');

    function validateLoginForm() {
      loginButton.disabled = !(
        emailInput.value.trim() &&
        passwordInput.value.trim() &&
        ageCheckbox.checked
      );
    }

    emailInput.addEventListener('input', validateLoginForm);
    passwordInput.addEventListener('input', validateLoginForm);
    ageCheckbox.addEventListener('change', validateLoginForm);

    // Il login è gestito da auth-handler.js, rimuoviamo il duplicato
    console.log('Index page loaded, auth handler will manage login');
  </script>
</body>
</html>