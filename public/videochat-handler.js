class VideoChatHandler {
    constructor() {
        this.socket = io('/', {
            transports: ['websocket'],
            upgrade: false
        });

        this.localStream = null;
        this.peerConnection = null;
        this.roomId = null;
        this.isAuthenticated = false;

        this.config = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' }
            ]
        };

        // Event handlers
        this.socket.on('connect', () => {
            console.log('Connected to signaling server');
            this.authenticateSocket();
        });
        this.socket.on('authenticated', this.handleAuthenticated.bind(this));
        this.socket.on('auth-error', this.handleAuthError.bind(this));
        this.socket.on('match-found', this.handleMatch.bind(this));
        this.socket.on('offer', this.handleOffer.bind(this));
        this.socket.on('answer', this.handleAnswer.bind(this));
        this.socket.on('ice-candidate', this.handleIceCandidate.bind(this));
        this.socket.on('call-ended', this.handleCallEnded.bind(this));
        this.socket.on('no-partners', this.handleNoPartners.bind(this));
        this.socket.on('videochat-error', this.handleVideochatError.bind(this));
    }

    authenticateSocket() {
        const token = localStorage.getItem('authToken');
        if (token) {
            this.socket.emit('authenticate', token);
        } else {
            console.error('No auth token found');
            window.location.href = '/';
        }
    }

    handleAuthenticated(data) {
        console.log('Socket authenticated:', data);
        this.isAuthenticated = true;
    }

    handleAuthError(error) {
        console.error('Socket auth error:', error);
        localStorage.removeItem('authToken');
        window.location.href = '/';
    }

    handleNoPartners(message) {
        console.log('No partners available:', message);
        alert('Nessun partner disponibile al momento. Riprova tra qualche minuto.');
    }

    handleVideochatError(error) {
        console.error('Videochat error:', error);
        alert('Errore videochat: ' + error);
    }

    async startSearch(preference = 'any') {
        if (!this.isAuthenticated) {
            console.error('Not authenticated');
            return;
        }

        if (!this.localStream) {
            await this.initializeMedia();
        }

        console.log('Starting search with preference:', preference);
        this.socket.emit('find-match', { preference });
    }

    async initializeMedia() {
        try {
            if (!this.localStream) {
                console.log('Inizializzazione media...');
                this.localStream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 1280, height: 720 },
                    audio: true
                });
                console.log('✅ Media stream ottenuto');
            }

            const localVideo = document.getElementById('localVideo');
            if (localVideo && this.localStream) {
                localVideo.srcObject = this.localStream;
                console.log('✅ Video locale collegato');
            }

            return this.localStream;
        } catch (err) {
            console.error('❌ Errore media:', err);

            // Mostra errore specifico
            let message = 'Errore accesso webcam: ';
            switch (err.name) {
                case 'NotAllowedError':
                    message += 'Permesso negato. Consenti l\'accesso alla webcam.';
                    break;
                case 'NotFoundError':
                    message += 'Webcam non trovata.';
                    break;
                case 'NotReadableError':
                    message += 'Webcam in uso da un\'altra app.';
                    break;
                default:
                    message += err.message;
            }

            alert(message);
            throw err;
        }
    }

    async handleMatch(data) {
        console.log('Match found:', data);
        this.roomId = data.roomId;
        await this.createPeerConnection();

        // Crea e invia offer
        try {
            const offer = await this.peerConnection.createOffer();
            await this.peerConnection.setLocalDescription(offer);
            this.socket.emit('webrtc-offer', {
                roomId: this.roomId,
                offer: offer
            });
        } catch (err) {
            console.error('Error creating offer:', err);
        }
    }

    async handleOffer(data) {
        console.log('Received offer:', data);
        if (!this.peerConnection) {
            await this.createPeerConnection();
        }

        try {
            await this.peerConnection.setRemoteDescription(data.offer);
            const answer = await this.peerConnection.createAnswer();
            await this.peerConnection.setLocalDescription(answer);
            this.socket.emit('webrtc-answer', {
                roomId: this.roomId,
                answer: answer
            });
        } catch (err) {
            console.error('Error handling offer:', err);
        }
    }

    async handleAnswer(data) {
        console.log('Received answer:', data);
        try {
            await this.peerConnection.setRemoteDescription(data.answer);
        } catch (err) {
            console.error('Error handling answer:', err);
        }
    }

    async createPeerConnection() {
        this.peerConnection = new RTCPeerConnection(this.config);

        if (this.localStream) {
            this.localStream.getTracks().forEach(track => {
                this.peerConnection.addTrack(track, this.localStream);
            });
        }

        this.peerConnection.ontrack = ({ streams: [stream] }) => {
            console.log('Received remote stream');
            const remoteVideo = document.getElementById('remoteVideo');
            if (remoteVideo) {
                remoteVideo.srcObject = stream;
            }
        };

        this.peerConnection.onicecandidate = ({ candidate }) => {
            if (candidate && this.roomId) {
                console.log('Sending ICE candidate');
                this.socket.emit('webrtc-ice-candidate', {
                    roomId: this.roomId,
                    candidate: candidate
                });
            }
        };

        this.peerConnection.onconnectionstatechange = () => {
            console.log('Connection state:', this.peerConnection.connectionState);
        };
    }

    handleCallEnded() {
        console.log('Call ended');
        if (this.peerConnection) {
            this.peerConnection.close();
            this.peerConnection = null;
        }

        const remoteVideo = document.getElementById('remoteVideo');
        if (remoteVideo) {
            remoteVideo.srcObject = null;
        }

        this.roomId = null;
    }

    async handleIceCandidate(data) {
        console.log('Received ICE candidate:', data);
        if (this.peerConnection && data.candidate) {
            try {
                await this.peerConnection.addIceCandidate(new RTCIceCandidate(data.candidate));
            } catch (err) {
                console.error('Error adding ICE candidate:', err);
            }
        }
    }

    endCall() {
        if (this.roomId) {
            this.socket.emit('end-call', { roomId: this.roomId });
        }
        this.handleCallEnded();
    }

    toggleVideo() {
        if (this.localStream) {
            const videoTrack = this.localStream.getVideoTracks()[0];
            if (videoTrack) {
                videoTrack.enabled = !videoTrack.enabled;
                return videoTrack.enabled;
            }
        }
        return false;
    }

    toggleAudio() {
        if (this.localStream) {
            const audioTrack = this.localStream.getAudioTracks()[0];
            if (audioTrack) {
                audioTrack.enabled = !audioTrack.enabled;
                return audioTrack.enabled;
            }
        }
        return false;
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.videoChatHandler = new VideoChatHandler();
});
