{"author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "name": "bintrees", "description": "Binary Search Trees", "version": "1.0.2", "keywords": ["binary tree", "red black tree", "red-black tree", "redblack tree"], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/vadimg/js_bintrees.git"}, "directories": {"lib": "lib"}, "main": "./index.js", "scripts": {"test": "nodeunit ./test/test_*.js && jshint lib/*.js index.js"}, "dependencies": {}, "devDependencies": {"nodeunit": "0.9.1", "jshint": "0.5.9", "underscore": "1.3.1", "reunion": "0.0.0"}}