import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  CognitoIdentityClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CognitoIdentityClient";
import { UnlinkIdentityInput } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface UnlinkIdentityCommandInput extends UnlinkIdentityInput {}
export interface UnlinkIdentityCommandOutput extends __MetadataBearer {}
declare const UnlinkIdentityCommand_base: {
  new (
    input: UnlinkIdentityCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UnlinkIdentityCommandInput,
    UnlinkIdentityCommandOutput,
    CognitoIdentityClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: UnlinkIdentityCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    UnlinkIdentityCommandInput,
    UnlinkIdentityCommandOutput,
    CognitoIdentityClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class UnlinkIdentityCommand extends UnlinkIdentityCommand_base {
  protected static __types: {
    api: {
      input: UnlinkIdentityInput;
      output: {};
    };
    sdk: {
      input: UnlinkIdentityCommandInput;
      output: UnlinkIdentityCommandOutput;
    };
  };
}
