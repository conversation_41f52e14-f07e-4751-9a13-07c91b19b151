import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CognitoIdentityClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CognitoIdentityClient";
import {
  SetPrincipalTagAttributeMapInput,
  SetPrincipalTagAttributeMapResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface SetPrincipalTagAttributeMapCommandInput
  extends SetPrincipalTagAttributeMapInput {}
export interface SetPrincipalTagAttributeMapCommandOutput
  extends SetPrincipalTagAttributeMapResponse,
    __MetadataBearer {}
declare const SetPrincipalTagAttributeMapCommand_base: {
  new (
    input: SetPrincipalTagAttributeMapCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    SetPrincipalTagAttributeMapCommandInput,
    SetPrincipalTagAttributeMapCommandOutput,
    CognitoIdentityClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: SetPrincipalTagAttributeMapCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    SetPrincipalTagAttributeMapCommandInput,
    SetPrincipalTagAttributeMapCommandOutput,
    CognitoIdentityClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class SetPrincipalTagAttributeMapCommand extends SetPrincipalTagAttributeMapCommand_base {
  protected static __types: {
    api: {
      input: SetPrincipalTagAttributeMapInput;
      output: SetPrincipalTagAttributeMapResponse;
    };
    sdk: {
      input: SetPrincipalTagAttributeMapCommandInput;
      output: SetPrincipalTagAttributeMapCommandOutput;
    };
  };
}
