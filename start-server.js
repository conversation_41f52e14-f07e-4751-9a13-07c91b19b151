#!/usr/bin/env node

/**
 * Script di avvio semplificato per VideoChat Couple
 */

const path = require('path');
const fs = require('fs');

// Verifica che il file server esista
const serverPath = path.join(__dirname, 'src', 'server.js');

if (!fs.existsSync(serverPath)) {
    console.error('❌ File server non trovato:', serverPath);
    process.exit(1);
}

console.log('🚀 Avvio VideoChat Couple Server...');
console.log('📁 Server path:', serverPath);
console.log('🌐 Porta:', process.env.PORT || 3001);

// Avvia il server
require(serverPath);
